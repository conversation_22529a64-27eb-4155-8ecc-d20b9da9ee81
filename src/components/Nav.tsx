import { NavLink } from "react-router-dom";

function Nav() {
  return (
    <nav className="flex justify-center mb-5 bg-gray-100 p-2.5 rounded-lg">
      <NavLink
        to="/"
        className={({ isActive }) =>
          `mx-4 no-underline font-medium py-2 px-3 rounded-md transition-colors duration-300 ${
            isActive
              ? "bg-blue-500 text-white"
              : "text-gray-700 hover:bg-gray-200"
          }`
        }
      >
        图像编辑
      </NavLink>
      <NavLink
        to="/try-on"
        className={({ isActive }) =>
          `mx-4 no-underline font-medium py-2 px-3 rounded-md transition-colors duration-300 ${
            isActive
              ? "bg-blue-500 text-white"
              : "text-gray-700 hover:bg-gray-200"
          }`
        }
      >
        虚拟试衣
      </NavLink>
      <NavLink
        to="/settings"
        className={({ isActive }) =>
          `mx-4 no-underline font-medium py-2 px-3 rounded-md transition-colors duration-300 ${
            isActive
              ? "bg-blue-500 text-white"
              : "text-gray-700 hover:bg-gray-200"
          }`
        }
      >
        系统设置
      </NavLink>
    </nav>
  );
}

export default Nav;
