import { NavLink } from "react-router-dom";
import "./Nav.css";

function Nav() {
  return (
    <nav className="nav">
      <NavLink to="/" className={({ isActive }) => (isActive ? "nav-link active" : "nav-link")}>
        图像编辑
      </NavLink>
      <NavLink to="/try-on" className={({ isActive }) => (isActive ? "nav-link active" : "nav-link")}>
        虚拟试衣
      </NavLink>
      <NavLink to="/settings" className={({ isActive }) => (isActive ? "nav-link active" : "nav-link")}>
        系统设置
      </NavLink>
    </nav>
  );
}

export default Nav;
