/* 通用组件样式类 */

/* 容器样式 */
.page-container {
  @apply max-w-3xl mx-auto p-5;
}

.page-title {
  @apply text-center text-gray-800 mb-2.5;
}

.page-subtitle {
  @apply text-center text-gray-600 mb-7;
}

/* 错误和进度消息样式 */
.error-message {
  @apply bg-red-50 border border-red-200 rounded-lg p-4 mb-5;
}

.error-message p {
  @apply text-red-700 m-0;
}

.progress-message {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-4 mb-5 flex items-center gap-2.5;
}

.progress-message p {
  @apply text-blue-800 m-0;
}

/* 配置区域样式 */
.config-section,
.upload-section,
.process-section,
.results-section {
  @apply bg-gray-50 rounded-xl p-5 mb-5 border border-gray-200;
}

.section-title {
  @apply flex items-center gap-2 mb-5 text-gray-700 text-xl;
}

/* 表单样式 */
.form-group {
  @apply mb-5;
}

.form-label {
  @apply block mb-2 font-medium text-gray-700;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full p-3 border border-gray-300 rounded-lg text-sm transition-all duration-150 ease-in-out box-border;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  @apply outline-none border-blue-400 shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)];
}

.form-textarea {
  @apply resize-y min-h-[80px] font-inherit;
}

/* 按钮样式 */
.btn-base {
  @apply inline-flex items-center gap-2 py-3 px-5 border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-150 ease-in-out;
}

.btn-secondary {
  @apply btn-base bg-gray-600 text-white;
}

.btn-secondary:hover:not(:disabled) {
  @apply bg-gray-700;
}

.btn-info {
  @apply btn-base bg-cyan-600 text-white;
}

.btn-info:hover:not(:disabled) {
  @apply bg-cyan-700;
}

.btn-primary {
  @apply btn-base bg-blue-500 text-white w-full justify-center;
}

.btn-primary:hover:not(:disabled) {
  @apply bg-blue-700;
}

.btn-success {
  @apply btn-base bg-green-600 text-white w-full justify-center;
}

.btn-success:hover:not(:disabled) {
  @apply bg-green-700;
}

.btn-warning {
  @apply btn-base bg-yellow-400 text-gray-900 text-xs py-2 px-3;
}

.btn-warning:hover:not(:disabled) {
  @apply bg-yellow-500;
}

.btn-info-small {
  @apply btn-base bg-cyan-600 text-white text-xs py-2 px-3 mt-2.5;
}

.btn-info-small:hover:not(:disabled) {
  @apply bg-cyan-700;
}

.btn-base:disabled {
  @apply opacity-60 cursor-not-allowed;
}

/* 文件选择和上传样式 */
.selected-file {
  @apply mt-2 text-gray-700 text-sm;
}

.upload-success {
  @apply bg-green-100 border border-green-300 rounded-lg p-4 mt-4;
}

.upload-success p {
  @apply text-green-800 m-0 break-all;
}

.task-info {
  @apply bg-gray-200 border border-gray-400 rounded-lg p-4 mt-4;
}

.task-info p {
  @apply text-gray-800 m-0 font-mono;
}

/* 结果展示样式 */
.result-images {
  @apply grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-5;
}

.result-item {
  @apply bg-white rounded-xl p-4 shadow-[0_2px_8px_rgba(0,0,0,0.1)];
}

.result-image {
  @apply w-full h-auto rounded-lg mb-2.5;
}

.current-image-info {
  @apply bg-blue-50 border border-blue-300 rounded-lg p-4 mb-5;
}

.current-image-info p {
  @apply my-1 text-gray-800;
}

.current-image-info strong {
  @apply text-blue-700;
}

.image-actions {
  @apply flex gap-2.5 mt-2.5 justify-center;
}

/* 信息文本样式 */
.info-text {
  @apply text-xs text-gray-600 mt-1;
}

.error-message-small {
  @apply mt-1 text-sm;
}

/* 左右分栏布局样式 */
.image-edit-layout {
  @apply flex gap-5 mt-5 min-h-[600px];
}

.config-panel {
  @apply flex-[0_0_40%] bg-gray-50 rounded-xl p-5 border border-gray-200 border-r-4 border-r-blue-500 overflow-y-auto max-h-[80vh];
}

.result-panel {
  @apply flex-1 bg-gray-50 rounded-xl p-5 border border-gray-200 border-l-4 border-l-green-600 flex flex-col;
}

.result-panel .section-title {
  @apply flex items-center gap-2 mb-5 text-gray-700 text-xl;
}

.result-content {
  @apply flex-1 flex flex-col justify-center items-center;
}

.result-placeholder {
  @apply flex justify-center items-center h-full min-h-[300px];
}

.placeholder-content {
  @apply text-center text-gray-500;
}

.placeholder-icon {
  @apply mb-4 opacity-50;
}

.placeholder-content p {
  @apply my-2;
}

.placeholder-hint {
  @apply text-sm text-gray-500 max-w-[300px] leading-relaxed;
}

.result-images {
  @apply w-full flex flex-col gap-5;
}

.result-item {
  @apply bg-white rounded-xl p-4 shadow-[0_2px_8px_rgba(0,0,0,0.1)] border border-gray-200;
}

.result-image {
  @apply w-full h-auto rounded-lg mb-4 max-h-[500px] object-contain;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .image-edit-layout {
    @apply flex-col;
  }

  .config-panel {
    @apply flex-none max-h-none;
  }

  .result-panel {
    @apply flex-none min-h-[400px];
  }
}

@media (max-width: 768px) {
  .image-edit-layout {
    @apply gap-4;
  }

  .config-panel,
  .result-panel {
    @apply p-4;
  }
}
