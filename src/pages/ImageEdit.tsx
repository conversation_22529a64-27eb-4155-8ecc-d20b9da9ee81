import { useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import { Upload, Image, Loader2, Download, Settings, Search } from "lucide-react";
import "../App.css";

function ImageEditPage() {
  const [selectedFile, setSelectedFile] = useState<string>("");
  // 豆包API密钥 - 硬编码配置，不再通过用户输入获取
  const douBaoApiKey = "2e3e1b1f-b6fb-46c7-a519-343d3146b88f";
  const [prompt, setPrompt] = useState<string>("");
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [uploadedUrl, setUploadedUrl] = useState<string>("");
  const [taskId, setTaskId] = useState<string>("");
  const [resultImages, setResultImages] = useState<string[]>([]);
  const [isQuerying, setIsQuerying] = useState(false);
  const [error, setError] = useState<string>("");
  const [progress, setProgress] = useState<string>("");

  // 图像编辑参数
  const [editStrength, setEditStrength] = useState<number>(0.7);
  const [guidanceScale, setGuidanceScale] = useState<number>(7.5);
  const [inferenceSteps, setInferenceSteps] = useState<number>(20);
  const [randomSeed, setRandomSeed] = useState<string>("");

  // 腾讯云COS配置 - 硬编码配置信息，不再通过用户输入获取
  const cosRegion = "ap-shanghai";
  const cosSecretId = "AKIDHdmkWCk6g3D9rMeiYCdpz1rVvINHxQis";
  const cosSecretKey = "3tWv64pOk9XzKXddWJyVwEOQL8oNn6IZ";
  const cosBucketName = "tennis-1258507500";
  const cosCustomDomain = "";

  // 手动输入的图片URL
  const [manualImageUrl, setManualImageUrl] = useState<string>("");
  const [urlValidationError, setUrlValidationError] = useState<string>("");

  const handleFilePathChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedFile(event.target.value);
    setError("");
  };

  // 验证图片URL格式
  const validateImageUrl = (url: string): boolean => {
    if (!url.trim()) return true;
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      const pathname = urlObj.pathname.toLowerCase();
      const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext));
      return hasImageExtension;
    } catch {
      return false;
    }
  };

  // 处理手动输入URL的变化
  const handleManualUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const url = event.target.value;
    setManualImageUrl(url);
    if (url.trim() && !validateImageUrl(url)) {
      setUrlValidationError("请输入有效的图片URL（支持jpg、png、gif等格式）");
    } else {
      setUrlValidationError("");
    }
  };

  const uploadFile = async () => {
    if (!selectedFile) {
      setError("请选择文件");
      return;
    }
    setIsUploading(true);
    setError("");
    setProgress("正在上传文件到腾讯云COS...");
    try {
      const url = await invoke<string>("upload_file_and_get_url", {
        region: cosRegion,
        secretId: cosSecretId,
        secretKey: cosSecretKey,
        bucketName: cosBucketName,
        customDomain: cosCustomDomain || null,
        filePath: selectedFile,
      });
      setUploadedUrl(url);
      setProgress("文件上传成功！");
      setTimeout(() => setProgress(""), 2000);
    } catch (err) {
      setError(`上传失败: ${err}`);
    } finally {
      setIsUploading(false);
    }
  };

  const startImageEdit = async () => {
    const imageUrlToUse = manualImageUrl.trim() || uploadedUrl;
    if (!imageUrlToUse || !prompt) {
      setError("请先上传文件或输入图片URL，并输入图像编辑提示词");
      return;
    }
    if (manualImageUrl.trim() && !validateImageUrl(manualImageUrl)) {
      setError("请输入有效的图片URL格式");
      return;
    }
    setIsProcessing(true);
    setError("");
    setProgress("正在进行图像编辑...");
    try {
      const seed = randomSeed.trim() ? parseInt(randomSeed) : null;
      const resultUrl = await invoke<string>("create_doubao_image_edit", {
        apiKey: douBaoApiKey,
        imageUrl: imageUrlToUse,
        prompt,
        strength: editStrength,
        guidanceScale,
        numInferenceSteps: inferenceSteps,
        seed,
      });
      setResultImages([resultUrl]);
      setProgress("图像编辑完成！");
      setIsProcessing(false);
      setTimeout(() => setProgress(""), 3000);
    } catch (err) {
      setError(`图像编辑失败: ${err}`);
      setIsProcessing(false);
    }
  };

  const queryTaskResult = async () => {
    if (!taskId) {
      setError("请先创建任务");
      return;
    }
    setIsQuerying(true);
    setError("");
    setProgress("正在查询图像编辑任务状态...");
    try {
      const result = await invoke<any>("get_doubao_image_edit_task_result", {
        apiKey: douBaoApiKey,
        taskId,
      });
      const status = result.status;
      if (status === "completed" || status === "success") {
        if (result.result && result.result.image_url) {
          setResultImages([result.result.image_url]);
          setProgress("图像编辑完成！");
          setTimeout(() => setProgress(""), 3000);
        } else {
          setError("图像编辑完成但未找到图片URL");
        }
      } else if (status === "failed" || status === "error") {
        const errorMsg = result.message || "图像编辑任务失败";
        setError(`任务失败: ${errorMsg}`);
      } else if (status === "pending" || status === "running" || status === "processing") {
        setProgress(`任务状态: ${status === "pending" ? "排队中" : "处理中"}，请稍后再次查询`);
        setTimeout(() => setProgress(""), 3000);
      } else {
        setError(`未知任务状态: ${status}`);
      }
    } catch (err) {
      setError(`查询任务状态失败: ${err}`);
    } finally {
      setIsQuerying(false);
    }
  };

  const downloadImage = async (url: string) => {
    try {
      window.open(url, '_blank');
    } catch (err) {
      setError(`打开图片失败: ${err}`);
    }
  };

  return (
    <div className="container">
      <h1>AI 图像编辑工具</h1>
      <p>基于字节跳动豆包大模型的图像编辑功能，使用腾讯云COS存储</p>

      {/* 左右分栏布局 */}
      <div className="image-edit-layout">
        {/* 左侧配置面板 */}
        <div className="config-panel">
          {/* 错误和进度提示信息 */}
          {error && (
            <div className="error-message">
              <p>错误: {error}</p>
            </div>
          )}

          {progress && (
            <div className="progress-message">
              <Loader2 className="spinner" />
              <p>{progress}</p>
            </div>
          )}

          {/* 图像编辑参数配置 */}
          <div className="config-section">
            <h2><Settings size={20} /> 图像编辑参数</h2>

            <div className="form-group">
              <label>编辑强度 (0.0-1.0):</label>
              <input
                type="number"
                min="0.0"
                max="1.0"
                step="0.1"
                value={editStrength}
                onChange={(e) => setEditStrength(parseFloat(e.target.value))}
                className="api-key-input"
                placeholder="0.7"
              />
              <p className="info-text">💡 值越高，编辑效果越明显</p>
            </div>

            <div className="form-group">
              <label>引导比例 (1.0-20.0):</label>
              <input
                type="number"
                min="1.0"
                max="20.0"
                step="0.5"
                value={guidanceScale}
                onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
                className="api-key-input"
                placeholder="7.5"
              />
              <p className="info-text">💡 控制模型对提示词的遵循程度</p>
            </div>

            <div className="form-group">
              <label>推理步数 (10-50):</label>
              <input
                type="number"
                min="10"
                max="50"
                step="1"
                value={inferenceSteps}
                onChange={(e) => setInferenceSteps(parseInt(e.target.value))}
                className="api-key-input"
                placeholder="20"
              />
              <p className="info-text">💡 步数越多，质量越高但速度越慢</p>
            </div>

            <div className="form-group">
              <label>随机种子 (可选):</label>
              <input
                type="text"
                value={randomSeed}
                onChange={(e) => setRandomSeed(e.target.value)}
                className="api-key-input"
                placeholder="留空则随机生成"
              />
              <p className="info-text">💡 相同种子可以生成相似的结果</p>
            </div>
          </div>

          {/* 图片来源配置 */}
          <div className="config-section">
            <h2><Upload size={20} /> 图片来源</h2>

            <div className="form-group">
              <label>图片URL（可选）:</label>
              <input
                type="text"
                value={manualImageUrl}
                onChange={handleManualUrlChange}
                placeholder="直接输入图片URL，例如: https://example.com/image.jpg"
                className="api-key-input"
              />
              {urlValidationError && (
                <div className="error-message error-message-small">
                  <p>{urlValidationError}</p>
                </div>
              )}
              <p className="info-text">
                💡 提示：可以直接输入图片URL，或者上传本地文件到腾讯云COS
              </p>
            </div>

            <div className="form-group">
              <label>本地图片文件路径:</label>
              <input
                type="text"
                value={selectedFile}
                onChange={handleFilePathChange}
                placeholder="请输入图片文件的完整路径，例如: /Users/<USER>/Pictures/image.jpg"
                className="api-key-input"
              />
            </div>

            <button
              type="button"
              onClick={uploadFile}
              disabled={!selectedFile || isUploading}
              className="upload-btn"
            >
              {isUploading ? <Loader2 className="spinner" /> : <Upload size={20} />}
              {isUploading ? "上传中..." : "上传到腾讯云COS"}
            </button>

            {uploadedUrl && (
              <div className="upload-success">
                <p>✅ 文件上传成功！URL: {uploadedUrl}</p>
              </div>
            )}
          </div>

          {/* 图像编辑提示词 */}
          <div className="config-section">
            <h2><Image size={20} /> 编辑提示词</h2>
            <div className="form-group">
              <label>图像编辑提示词:</label>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="请输入图像编辑的提示词，例如：将天空改为夕阳，保持其他部分不变"
                className="prompt-textarea"
                rows={3}
              />
            </div>

            <button
              type="button"
              onClick={startImageEdit}
              disabled={(!uploadedUrl && !manualImageUrl.trim()) || !prompt || isProcessing || !!urlValidationError}
              className="process-btn"
            >
              {isProcessing ? <Loader2 className="spinner" /> : <Image size={20} />}
              {isProcessing ? "编辑中..." : "开始图像编辑"}
            </button>
          </div>

          {/* 任务查询功能 */}
          <div className="config-section">
            <h2><Search size={20} /> 任务查询</h2>
            <div className="form-group">
              <label>任务ID:</label>
              <input
                type="text"
                value={taskId}
                onChange={(e) => setTaskId(e.target.value)}
                placeholder="任务ID会在创建任务后自动填入，也可手动输入"
                className="api-key-input"
              />
            </div>

            {taskId && (
              <button
                type="button"
                onClick={queryTaskResult}
                disabled={!taskId || isQuerying}
                className="query-btn"
              >
                {isQuerying ? <Loader2 className="spinner" /> : <Search size={16} />}
                {isQuerying ? "查询中..." : "查询任务结果"}
              </button>
            )}
          </div>
        </div>

        {/* 右侧结果展示区域 */}
        <div className="result-panel">
          <h2><Image size={20} /> 编辑结果</h2>

          {/* 当前图片来源信息 */}
          {(manualImageUrl.trim() || uploadedUrl) && (
            <div className="current-image-info">
              <p><strong>当前图片来源：</strong></p>
              {manualImageUrl.trim() ? (
                <p>🔗 手动输入URL: {manualImageUrl}</p>
              ) : (
                <p>📁 上传文件URL: {uploadedUrl}</p>
              )}
            </div>
          )}

          {/* 结果展示区域 */}
          <div className="result-content">
            {resultImages.length > 0 ? (
              <div className="result-images">
                {resultImages.map((url, index) => (
                  <div key={index} className="result-item">
                    <img
                      src={url}
                      alt={`编辑结果 ${index + 1}`}
                      className="result-image"
                    />
                    <div className="image-actions">
                      <button
                        onClick={() => downloadImage(url)}
                        className="download-btn"
                        type="button"
                      >
                        <Download size={16} />
                        查看原图
                      </button>
                      <button
                        onClick={() => window.open(url, '_blank')}
                        className="download-btn"
                        type="button"
                      >
                        <Image size={16} />
                        在新窗口打开
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="result-placeholder">
                <div className="placeholder-content">
                  <Image size={48} className="placeholder-icon" />
                  <p>编辑结果将在这里显示</p>
                  <p className="placeholder-hint">
                    请先配置参数、选择图片并输入提示词，然后点击"开始图像编辑"
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ImageEditPage;