import { useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import { Loader2, Image } from "lucide-react";

function TryOnPage() {
  const [modelImageUrl, setModelImageUrl] = useState("");
  const [garmentImageUrl, setGarmentImageUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [resultImageUrl, setResultImageUrl] = useState("");
  const [error, setError] = useState("");

  // 阿里云API密钥 - 硬编码配置，不再通过用户输入获取
  // 密钥已从前端界面隐藏，直接在代码中设置
  const aliyunApiKey = "sk-52033e5a00504594befe248ad868a029";

  const handleStartTryOn = async () => {
    // API密钥已硬编码，只需检查图片URL是否提供
    if (!modelImageUrl || !garmentImageUrl) {
      setError("请提供模特图片URL和服装图片URL");
      return;
    }

    setIsProcessing(true);
    setError("");
    setResultImageUrl("");

    try {
      const result: any = await invoke("start_aliyun_tryon", {
        apiKey: aliyunApiKey,
        imageUrl: modelImageUrl,
        garmentUrl: garmentImageUrl,
      });

      if (result.output.task_status === "SUCCEEDED" && result.output.image_url) {
        setResultImageUrl(result.output.image_url);
      } else {
        setError(`处理失败: ${result.output.task_status}`);
      }
    } catch (err) {
      setError(`发生错误: ${err}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div>
      <h1>AI 虚拟试衣</h1>
      <p>提供模特图片和服装图片，查看AI虚拟试衣效果</p>
      
      {/* 阿里云API密钥输入框已隐藏 - 密钥已硬编码到代码中 */}
      {/*
      原API密钥输入框已移除，密钥现在直接在代码中设置：
      - 阿里云API密钥: sk-52033e5a00504594befe248ad868a029
      */}

      <div className="form-group">
        <label>模特图片URL:</label>
        <input
          type="text"
          value={modelImageUrl}
          onChange={(e) => setModelImageUrl(e.target.value)}
          placeholder="https://example.com/model.jpg"
          className="api-key-input"
        />
      </div>

      <div className="form-group">
        <label>服装图片URL:</label>
        <input
          type="text"
          value={garmentImageUrl}
          onChange={(e) => setGarmentImageUrl(e.target.value)}
          placeholder="https://example.com/garment.jpg"
          className="api-key-input"
        />
      </div>

      <button
        type="button"
        onClick={handleStartTryOn}
        disabled={!modelImageUrl || !garmentImageUrl || isProcessing}
        className="process-btn"
      >
        {isProcessing ? <Loader2 className="spinner" /> : <Image size={20} />}
        {isProcessing ? "处理中..." : "开始AI虚拟试衣"}
      </button>

      {error && (
        <div className="error-message">
          <p>错误: {error}</p>
        </div>
      )}

      {resultImageUrl && (
        <div className="results-section">
          <h2>虚拟试衣结果</h2>
          <img src={resultImageUrl} alt="AI虚拟试衣结果" className="result-image" />
        </div>
      )}
    </div>
  );
}

export default TryOnPage;
