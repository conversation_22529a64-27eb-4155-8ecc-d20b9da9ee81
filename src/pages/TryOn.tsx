import { useState } from "react";
import { invoke } from "@tauri-apps/api/core";
import { Loader2, Image } from "lucide-react";

function TryOnPage() {
  const [modelImageUrl, setModelImageUrl] = useState("");
  const [garmentImageUrl, setGarmentImageUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [resultImageUrl, setResultImageUrl] = useState("");
  const [error, setError] = useState("");

  // 阿里云API密钥 - 硬编码配置，不再通过用户输入获取
  // 密钥已从前端界面隐藏，直接在代码中设置
  const aliyunApiKey = "sk-52033e5a00504594befe248ad868a029";

  const handleStartTryOn = async () => {
    // API密钥已硬编码，只需检查图片URL是否提供
    if (!modelImageUrl || !garmentImageUrl) {
      setError("请提供模特图片URL和服装图片URL");
      return;
    }

    setIsProcessing(true);
    setError("");
    setResultImageUrl("");

    try {
      const result: any = await invoke("start_aliyun_tryon", {
        apiKey: aliyunApiKey,
        imageUrl: modelImageUrl,
        garmentUrl: garmentImageUrl,
      });

      if (result.output.task_status === "SUCCEEDED" && result.output.image_url) {
        setResultImageUrl(result.output.image_url);
      } else {
        setError(`处理失败: ${result.output.task_status}`);
      }
    } catch (err) {
      setError(`发生错误: ${err}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-8">
      <h1 className="text-center text-gray-800 mb-2.5 text-3xl font-bold">AI 虚拟试衣</h1>
      <p className="text-center text-gray-600 mb-7">提供模特图片和服装图片，查看AI虚拟试衣效果</p>

      {/* 阿里云API密钥输入框已隐藏 - 密钥已硬编码到代码中 */}
      {/*
      原API密钥输入框已移除，密钥现在直接在代码中设置：
      - 阿里云API密钥: sk-52033e5a00504594befe248ad868a029
      */}

      <div className="mb-5">
        <label className="block mb-2 font-medium text-gray-700">模特图片URL:</label>
        <input
          type="text"
          value={modelImageUrl}
          onChange={(e) => setModelImageUrl(e.target.value)}
          placeholder="https://example.com/model.jpg"
          className="w-full p-3 border border-gray-300 rounded-lg text-sm transition-all duration-150 ease-in-out box-border focus:outline-none focus:border-blue-400 focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]"
        />
      </div>

      <div className="mb-5">
        <label className="block mb-2 font-medium text-gray-700">服装图片URL:</label>
        <input
          type="text"
          value={garmentImageUrl}
          onChange={(e) => setGarmentImageUrl(e.target.value)}
          placeholder="https://example.com/garment.jpg"
          className="w-full p-3 border border-gray-300 rounded-lg text-sm transition-all duration-150 ease-in-out box-border focus:outline-none focus:border-blue-400 focus:shadow-[0_0_0_0.2rem_rgba(0,123,255,0.25)]"
        />
      </div>

      <button
        type="button"
        onClick={handleStartTryOn}
        disabled={!modelImageUrl || !garmentImageUrl || isProcessing}
        className="inline-flex items-center gap-2 py-3 px-5 border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-150 ease-in-out bg-green-600 text-white w-full justify-center hover:bg-green-700 disabled:opacity-60 disabled:cursor-not-allowed"
      >
        {isProcessing ? <Loader2 className="spinner" /> : <Image size={20} />}
        {isProcessing ? "处理中..." : "开始AI虚拟试衣"}
      </button>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-5 mt-5">
          <p className="text-red-700 m-0">错误: {error}</p>
        </div>
      )}

      {resultImageUrl && (
        <div className="bg-gray-50 rounded-xl p-5 mb-5 border border-gray-200 mt-5">
          <h2 className="flex items-center gap-2 mb-5 text-gray-700 text-xl">虚拟试衣结果</h2>
          <img src={resultImageUrl} alt="AI虚拟试衣结果" className="w-full h-auto rounded-lg mb-2.5" />
        </div>
      )}
    </div>
  );
}

export default TryOnPage;
