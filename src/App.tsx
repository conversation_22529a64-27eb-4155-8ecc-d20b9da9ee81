import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import SettingsPage from "./pages/Settings";
import TryOnPage from "./pages/TryOn";
import ImageEditPage from "./pages/ImageEdit";
import Nav from "./components/Nav";

function App() {
  return (
    <Router>
      <main className="max-w-4xl mx-auto p-8">
        <Nav />
        <Routes>
          <Route path="/" element={<ImageEditPage />} />
          <Route path="/image-edit" element={<ImageEditPage />} />
          <Route path="/try-on" element={<TryOnPage />} />
          <Route path="/settings" element={<SettingsPage />} />
        </Routes>
      </main>
    </Router>
  );
}

export default App;
