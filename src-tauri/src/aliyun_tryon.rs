use serde::{Serialize, Deserialize};
use reqwest::Client;

#[derive(Serialize)]
struct TryOnInput<'a> {
    model: &'a str,
    image_url: &'a str,
    garment_url: &'a str,
}

#[derive(Serialize)]
struct TryOnPayload<'a> {
    input: TryOnInput<'a>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct TryOnOutput {
    pub task_id: String,
    pub task_status: String,
    pub image_url: Option<String>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct TryOnResponse {
    pub output: TryOnOutput,
    pub request_id: String,
}

pub async fn call_aliyun_tryon(api_key: &str, image_url: &str, garment_url: &str) -> Result<TryOnResponse, reqwest::Error> {
    let client = Client::new();
    let url = "https://dashscope.aliyuncs.com/api/v1/services/vision/image-generation/aitryon";

    let payload = TryOnPayload {
        input: TryOnInput {
            model: "aitryon-plus",
            image_url,
            garment_url,
        },
    };

    let response = client.post(url)
        .header("Authorization", format!("Bearer {}", api_key))
        .json(&payload)
        .send()
        .await?;

    response.json::<TryOnResponse>().await
}
